"use client"

import { useR<PERSON>, useState, useEffect } from "react"
import { motion, useAnimation, AnimatePresence } from "framer-motion"
import { useInView } from "react-intersection-observer"
import dynamic from "next/dynamic"
import { Container } from "@/components/ui/container"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { BRAND } from "@/lib/constants"
import {
  ArrowRight,
  Beaker,
  Target,
  Zap,
  LineChart,
  Lightbulb,
  FlaskConical,
  Microscope,
  BarChart,
  Play,
  Pause,
  RefreshCw
} from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { ackley, generateFunctionGrid, findMinimum } from "@/lib/test-functions"

// Dynamically import Plotly to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot: any = dynamic(() => import("react-plotly.js"), { ssr: false })

// Optimization points interface
interface OptimizationPoint {
  x: number
  y: number
  z: number
  iteration: number
  isNextPoint?: boolean
  isOptimum?: boolean
  isLHSPoint?: boolean
  isBayesianPoint?: boolean
  isInitialSample?: boolean
}

// Function to generate optimization points - completely independent from case studies
function generateOptimizationPoints(): OptimizationPoint[] {
  const points: OptimizationPoint[] = []
  const range: [number, number] = [-5, 5] // Use a smaller range for simpler visualization

  // Define objective function values that show improvement over iterations
  // All points are within the range [-5, 5] to match the Ackley function visualization

  // Initial samples (higher values = worse performance)
  // Using Latin Hypercube Sampling pattern within the range [-4, 4]
  // Values provided directly to ensure they match the expected values
  const initialObjectives = [
    { x: -3.2, y: 2.1, z: 9.329444788616398 },
    { x: 2.8, y: -3.5, z: 11.399790250734858 },
    { x: -1.5, y: -1.8, z: 7.651488172793533 },
    { x: 3.7, y: 0.9, z: 9.761988167219673 },
    { x: 0.8, y: 3.2, z: 8.812024978743553 }
  ]

  // Bayesian optimization samples (progressively better values)
  // Points get closer to (0,0) which is the global minimum of Ackley function
  const bayesianObjectives = [
    { x: 0.8, y: -1.2, z: 5.046309234254689 },
    { x: -0.5, y: 0.7, z: 4.489512886420172 },
    { x: 0.3, y: 0.2, z: 2.7125220698330463 },
    { x: 0.1, y: -0.1, z: 0.8686089961219463 } // Best point, closest to global minimum at (0,0)
  ]

  // Add initial sampling points
  initialObjectives.forEach((point, i) => {
    points.push({
      x: point.x,
      y: point.y,
      z: point.z,
      iteration: i + 1, // Iterations 1-5 are initial points
      isInitialSample: true, // Mark as initial sample for distinctive color
      isLHSPoint: true,
      isBayesianPoint: false,
      isOptimum: false,
      isNextPoint: false
    })
  })

  // Add Bayesian optimization points
  bayesianObjectives.forEach((point, i) => {
    points.push({
      x: point.x,
      y: point.y,
      z: point.z,
      iteration: i + 6, // Iterations 6-9 are Bayesian points
      isInitialSample: false,
      isLHSPoint: false,
      isBayesianPoint: true,
      // The 9th iteration (index 3) is the optimal point
      isOptimum: i === 3,
      // The last point is also the next suggested point
      isNextPoint: i === 3
    })
  })

  return points
}

// Case studies data
const caseStudies = [
  {
    title: "Materials Lab Breakthrough",
    description:
      "Reduced experimental runs from 50 to 15 while achieving optimal material properties for a new composite material.",
    icon: <FlaskConical className="text-primary size-8" />,
    optimizationData: {
      startingPoints: 50,
      finalPoints: 15,
      improvementPercent: 70,
      timeReduction: "65%"
    }
  },
  {
    title: "Enzyme Engineering Success",
    description:
      "Optimized enzyme performance in just 3 weeks instead of the typical 3-month timeline, saving over $200,000 in lab costs.",
    icon: <Microscope className="text-primary size-8" />,
    optimizationData: {
      startingPoints: 40,
      finalPoints: 12,
      improvementPercent: 85,
      timeReduction: "75%"
    }
  },
  {
    title: "Process Optimization Win",
    description:
      "Improved manufacturing yield by 32% while reducing energy consumption by 18% through intelligent parameter optimization.",
    icon: <BarChart className="text-primary size-8" />,
    optimizationData: {
      startingPoints: 35,
      finalPoints: 10,
      improvementPercent: 32,
      timeReduction: "50%"
    }
  }
]

// Types for the animated elements
interface ExperimentPoint {
  id: number
  x: number
  y: number
  size: number
  opacity: number
  color: string
  isOptimal: boolean
  caseStudyIndex?: number
}

interface OptimizationPath {
  points: { x: number; y: number }[]
  progress: number
  caseStudyIndex: number
}

export default function EnhancedOptimizationShowcase() {
  // Refs for container dimensions
  const containerRef = useRef<HTMLDivElement>(null)
  const plotRef = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })

  // Animation controls
  const controls = useAnimation()
  const [ref, inView] = useInView({
    triggerOnce: true, // Only trigger once when the section comes into view
    threshold: 0.1
  })

  // State for visualization
  const [viewMode, setViewMode] = useState<"3d" | "contour">("3d")
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentIteration, setCurrentIteration] = useState(0)
  const [activeCaseStudy, setActiveCaseStudy] = useState(0)

  // State for optimization data
  const [optimizationPoints, setOptimizationPoints] = useState<
    OptimizationPoint[]
  >([])

  // Generate the function grid for visualization
  // Use a smaller range for simpler visualization
  const range: [number, number] = [-5, 5]
  const { x, y, z } = generateFunctionGrid(ackley, range, range, 50)

  // Update dimensions on resize
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        setDimensions({
          width: containerRef.current.offsetWidth,
          height: containerRef.current.offsetHeight
        })
      }
    }

    updateDimensions()
    window.addEventListener("resize", updateDimensions)

    return () => {
      window.removeEventListener("resize", updateDimensions)
    }
  }, [])

  // Initialize animation and data immediately
  useEffect(() => {
    // Generate optimization points - completely independent from case studies
    const points = generateOptimizationPoints()
    setOptimizationPoints(points)
    setCurrentIteration(0)

    // Start with animation visible
    controls.start("visible")

    // Auto-play animation after a short delay when component mounts
    const timer = setTimeout(() => {
      setIsPlaying(true)
    }, 500)

    return () => clearTimeout(timer)
  }, [controls]) // Only run once on mount

  // Handle play/pause
  const togglePlayPause = () => {
    if (isPlaying) {
      setIsPlaying(false)
    } else {
      setIsPlaying(true)

      // If we're at the end, restart
      if (currentIteration >= 10) {
        setCurrentIteration(0)
      }
    }
  }

  // Animation effect
  useEffect(() => {
    if (!isPlaying) return

    const interval = setInterval(() => {
      setCurrentIteration(prev => {
        // If we're at the last iteration (9), stop playing
        if (prev >= 9) {
          setIsPlaying(false)
          return prev
        }

        // Otherwise, move to the next iteration
        return prev + 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isPlaying])

  // Reset animation
  const resetAnimation = () => {
    setCurrentIteration(0)
    setIsPlaying(false)
    setActiveCaseStudy(0)
  }

  // Filter points based on current iteration - show all case studies at once
  const visiblePoints = optimizationPoints.filter(
    point => point.iteration <= currentIteration
  )

  // Independent animation for case studies - start immediately
  useEffect(() => {
    // Start with first case study
    setActiveCaseStudy(0)

    // Cycle through case studies every 8 seconds
    const cycleInterval = setInterval(() => {
      setActiveCaseStudy(prev => (prev + 1) % caseStudies.length)
    }, 8000)

    return () => clearInterval(cycleInterval)
  }, [])

  // Variants for animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10,
        duration: 0.5
      }
    }
  }

  const cardVariants = {
    initial: { scale: 1 },
    hover: {
      scale: 1.03,
      boxShadow:
        "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"
    },
    tap: { scale: 0.98 },
    active: {
      scale: 1.05,
      boxShadow:
        "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      borderColor: "var(--primary)",
      borderWidth: "2px"
    }
  }

  // Spring animation for case study transitions
  const springTransition = {
    type: "spring",
    stiffness: 300,
    damping: 30
  }

  return (
    <section className="py-16 md:py-24">
      <Container>
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="visible" // Start visible immediately
          animate={controls}
          className="text-center"
        >
          <motion.h2
            variants={itemVariants}
            className="from-primary mb-4 bg-gradient-to-r to-blue-600 bg-clip-text text-3xl font-bold text-transparent md:text-4xl"
          >
            Transforming Experimental Optimization
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-muted-foreground mx-auto max-w-2xl text-base md:text-lg"
          >
            Our advanced {BRAND.NAME} platform is engineered for scientists and
            engineers pushing the boundaries of research, materials development,
            and process optimization.
          </motion.p>

          {/* Main visualization section */}
          <motion.div variants={itemVariants} className="mx-auto mt-12">
            {/* Optimization Visualization */}
            <motion.div
              ref={containerRef}
              className="relative mx-auto overflow-hidden rounded-xl shadow-xl"
              initial={{ opacity: 1, y: 0 }} // Start fully visible
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0 }} // No transition needed
            >
              <div
                className="w-full bg-gradient-to-br from-gray-50 to-gray-100"
                style={{ height: "500px" }}
              >
                <div ref={plotRef} className="size-full">
                  {viewMode === "3d" ? (
                    <Plot
                      data={[
                        // Surface plot - this remains constant throughout iterations
                        {
                          type: "surface",
                          x,
                          y,
                          z,
                          colorscale: "Blues",
                          opacity: 0.8,
                          showscale: true, // Show color scale to help understand the function values
                          cmin: 0, // Minimum color value
                          cmax: 15, // Maximum color value
                          contours: {
                            z: {
                              show: true,
                              usecolormap: true,
                              highlightcolor: "#fff",
                              project: { z: true }
                            }
                          },
                          hovertemplate:
                            "x: %{x}<br>" +
                            "y: %{y}<br>" +
                            "z: %{z}<extra></extra>"
                        },
                        // Optimization points - these change with iterations
                        {
                          type: "scatter3d",
                          mode: "markers",
                          x: optimizationPoints
                            .filter(p => p.iteration <= currentIteration)
                            .map(p => p.x),
                          y: optimizationPoints
                            .filter(p => p.iteration <= currentIteration)
                            .map(p => p.y),
                          z: optimizationPoints
                            .filter(p => p.iteration <= currentIteration)
                            .map(p => p.z),
                          marker: {
                            size: optimizationPoints
                              .filter(p => p.iteration <= currentIteration)
                              .map(p => {
                                if (p.isOptimum) return 14 // Larger size for optimal point
                                if (p.isNextPoint) return 12 // Medium-large for next point
                                if (p.isBayesianPoint) return 10 // Medium for Bayesian points
                                return 8 // Standard size for LHS points
                              }),
                            color: optimizationPoints
                              .filter(p => p.iteration <= currentIteration)
                              .map(p => {
                                if (p.isOptimum) return "#10b981" // Green for optimal point
                                if (p.isInitialSample) return "#f59e0b" // Orange for initial sampling points
                                if (p.isBayesianPoint) return "#8b5cf6" // Purple for Bayesian optimization points
                                return "#64748b" // Default gray
                              }),
                            opacity: 0.9,
                            line: {
                              width: 1.5,
                              color: "white"
                            },
                            symbol: optimizationPoints
                              .filter(p => p.iteration <= currentIteration)
                              .map(p =>
                                p.isOptimum
                                  ? "diamond"
                                  : p.isNextPoint
                                    ? "star"
                                    : "circle"
                              )
                          },
                          hovertemplate:
                            "x: %{x}<br>" +
                            "y: %{y}<br>" +
                            "z: %{z}<br>" +
                            "Iteration: %{text}<br>" +
                            "%{customdata}<extra></extra>",
                          text: optimizationPoints
                            .filter(p => p.iteration <= currentIteration)
                            .map(p => p.iteration.toString()),
                          customdata: optimizationPoints
                            .filter(p => p.iteration <= currentIteration)
                            .map(p => {
                              if (p.isOptimum) return "Optimal Solution"
                              if (p.isInitialSample)
                                return "Initial Sampling Point"
                              if (p.isBayesianPoint)
                                return "Bayesian Optimization Point"
                              return "Experiment Point"
                            })
                        }
                      ]}
                      layout={{
                        title: "",
                        autosize: true,
                        margin: { l: 10, r: 80, b: 10, t: 10 }, // Increased right margin for color bar
                        paper_bgcolor: "rgba(0,0,0,0)",
                        plot_bgcolor: "rgba(0,0,0,0)",
                        scene: {
                          xaxis: {
                            title: "x",
                            titlefont: { size: 12 },
                            tickfont: { size: 10 },
                            range: [-5, 5]
                          },
                          yaxis: {
                            title: "y",
                            titlefont: { size: 12 },
                            tickfont: { size: 10 },
                            range: [-5, 5]
                          },
                          zaxis: {
                            title: "f(x,y)",
                            titlefont: { size: 12 },
                            tickfont: { size: 10 }
                            // Removed fixed range to allow auto-scaling
                          },
                          camera: {
                            eye: { x: 1.75, y: 1.75, z: 1.25 },
                            center: { x: 0, y: 0, z: 0 } // Reset to default center
                          }
                        },
                        font: {
                          family: "Inter, system-ui, sans-serif",
                          size: 12
                        }
                      }}
                      config={{
                        displayModeBar: false,
                        responsive: true
                      }}
                      style={{ width: "100%", height: "100%" }}
                    />
                  ) : (
                    <Plot
                      data={[
                        // Contour plot - this remains constant throughout iterations
                        {
                          type: "contour",
                          x,
                          y,
                          z,
                          colorscale: "Blues",
                          showscale: true, // Show color scale to help understand the function values
                          cmin: 0, // Minimum color value
                          cmax: 15, // Maximum color value
                          contours: {
                            coloring: "heatmap",
                            showlabels: true
                          },
                          hovertemplate:
                            "x: %{x}<br>" +
                            "y: %{y}<br>" +
                            "z: %{z}<extra></extra>"
                        },
                        // Optimization points - these change with iterations
                        {
                          type: "scatter",
                          mode: "markers",
                          x: optimizationPoints
                            .filter(p => p.iteration <= currentIteration)
                            .map(p => p.x),
                          y: optimizationPoints
                            .filter(p => p.iteration <= currentIteration)
                            .map(p => p.y),
                          marker: {
                            size: optimizationPoints
                              .filter(p => p.iteration <= currentIteration)
                              .map(p => {
                                if (p.isOptimum) return 18 // Larger size for optimal point
                                if (p.isNextPoint) return 16 // Medium-large for next point
                                if (p.isBayesianPoint) return 14 // Medium for Bayesian points
                                return 12 // Standard size for LHS points
                              }),
                            color: optimizationPoints
                              .filter(p => p.iteration <= currentIteration)
                              .map(p => {
                                if (p.isOptimum) return "#10b981" // Green for optimal point
                                if (p.isInitialSample) return "#f59e0b" // Orange for initial sampling points
                                if (p.isBayesianPoint) return "#8b5cf6" // Purple for Bayesian optimization points
                                return "#64748b" // Default gray
                              }),
                            opacity: 0.9,
                            line: {
                              width: 2,
                              color: "white"
                            },
                            symbol: optimizationPoints
                              .filter(p => p.iteration <= currentIteration)
                              .map(p =>
                                p.isOptimum
                                  ? "diamond"
                                  : p.isNextPoint
                                    ? "star"
                                    : "circle"
                              )
                          },
                          hovertemplate:
                            "x: %{x}<br>" +
                            "y: %{y}<br>" +
                            "z: %{z}<br>" +
                            "Iteration: %{text}<br>" +
                            "%{customdata}<extra></extra>",
                          text: optimizationPoints
                            .filter(p => p.iteration <= currentIteration)
                            .map(p => p.iteration.toString()),
                          customdata: optimizationPoints
                            .filter(p => p.iteration <= currentIteration)
                            .map(p => {
                              if (p.isOptimum) return "Optimal Solution"
                              if (p.isInitialSample)
                                return "Initial Sampling Point"
                              if (p.isBayesianPoint)
                                return "Bayesian Optimization Point"
                              return "Experiment Point"
                            })
                        }
                      ]}
                      layout={{
                        title: "",
                        autosize: true,
                        margin: { l: 40, r: 80, b: 40, t: 10 }, // Increased right margin for color bar
                        paper_bgcolor: "rgba(0,0,0,0)",
                        plot_bgcolor: "rgba(0,0,0,0)",
                        xaxis: {
                          title: "x",
                          titlefont: { size: 12 },
                          tickfont: { size: 10 },
                          range: [-5, 5]
                        },
                        yaxis: {
                          title: "y",
                          titlefont: { size: 12 },
                          tickfont: { size: 10 },
                          range: [-5, 5]
                        },
                        font: {
                          family: "Inter, system-ui, sans-serif",
                          size: 12
                        }
                      }}
                      config={{
                        displayModeBar: false,
                        responsive: true
                      }}
                      style={{ width: "100%", height: "100%" }}
                    />
                  )}
                </div>
              </div>

              {/* Controls and info section below the plot */}
              <div className="border-t bg-white p-4">
                <div className="mb-4 rounded-md border bg-gradient-to-br from-white to-gray-50/80 p-4 text-sm shadow-sm">
                  <p className="mb-2">
                    This visualization shows how Bayesian optimization
                    efficiently navigates the complex Ackley function to find
                    the global minimum in minimal experiments. The algorithm
                    balances exploration and exploitation to converge on optimal
                    solutions with significantly fewer experimental runs.
                  </p>

                  {/* Color legend */}
                  <div className="mt-3 flex flex-wrap items-center gap-x-4 gap-y-2 text-xs">
                    <div className="flex items-center">
                      <div className="mr-1.5 size-3 rounded-full bg-[#f59e0b]"></div>
                      <span>Initial Sampling</span>
                    </div>
                    <div className="flex items-center">
                      <div className="mr-1.5 size-3 rounded-full bg-[#8b5cf6]"></div>
                      <span>Bayesian Optimization</span>
                    </div>
                    <div className="flex items-center">
                      <div className="mr-1.5 size-3 rounded-full bg-[#10b981]"></div>
                      <span>Optimal Solution</span>
                    </div>
                  </div>
                </div>

                {/* Controls */}
                <div className="flex flex-wrap items-center justify-between gap-3">
                  <div className="flex items-center gap-3">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="outline"
                        size="icon"
                        className="size-9 sm:size-10"
                        onClick={togglePlayPause}
                      >
                        {isPlaying ? (
                          <Pause className="size-4 sm:size-5" />
                        ) : (
                          <Play className="size-4 sm:size-5" />
                        )}
                      </Button>
                    </motion.div>

                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="outline"
                        size="icon"
                        className="size-9 sm:size-10"
                        onClick={resetAnimation}
                      >
                        <RefreshCw className="size-4 sm:size-5" />
                      </Button>
                    </motion.div>

                    <div className="ml-2 text-sm font-medium sm:text-base">
                      Iteration: {currentIteration} / 9
                    </div>
                  </div>

                  <div className="flex items-center">
                    <span className="mr-2 text-sm font-medium sm:text-base">
                      View Mode:
                    </span>
                    <motion.div
                      className="flex rounded-md border"
                      whileHover={{
                        boxShadow:
                          "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
                      }}
                    >
                      <Button
                        variant={viewMode === "3d" ? "default" : "ghost"}
                        size="sm"
                        className="rounded-r-none text-sm"
                        onClick={() => setViewMode("3d")}
                      >
                        3D Surface
                      </Button>
                      <Button
                        variant={viewMode === "contour" ? "default" : "ghost"}
                        size="sm"
                        className="rounded-l-none text-sm"
                        onClick={() => setViewMode("contour")}
                      >
                        Contour
                      </Button>
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Case Studies Section - now separate from the plot */}
            {/*
            <motion.div
              variants={itemVariants}
              className="mt-16"
              initial={{ opacity: 1 }} // Start fully visible
              animate={{ opacity: 1 }}
              transition={{ duration: 0 }} // No transition delay
            >
              <motion.h3
                className="from-primary mb-6 bg-gradient-to-r to-blue-600 bg-clip-text text-center text-xl font-semibold text-transparent md:text-2xl"
                initial={{ opacity: 1, y: 0 }} // Start fully visible
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0 }} // No transition needed
              >
                Real-World Optimization Success Stories
              </motion.h3>

              <div className="grid gap-6 md:grid-cols-3">
                {caseStudies.map((study, index) => (
                  <motion.div
                    key={index}
                    variants={cardVariants}
                    initial="initial"
                    animate={activeCaseStudy === index ? "active" : "initial"}
                    whileHover="hover"
                    whileTap="tap"
                    className="overflow-hidden rounded-xl border bg-gradient-to-br from-white to-gray-50/80 shadow-md transition-all"
                    onClick={() => setActiveCaseStudy(index)}
                    layoutId={`case-study-${index}`}
                  >
                    <motion.div
                      className="from-primary/10 to-primary/5 bg-gradient-to-r p-5"
                      layoutId={`case-study-header-${index}`}
                    >
                      <motion.div
                        className="mb-3 flex items-center gap-3"
                        layoutId={`case-study-title-${index}`}
                      >
                        <motion.div
                          className="bg-primary/10 flex size-12 items-center justify-center rounded-full"
                          whileHover={{ rotate: 5, scale: 1.05 }}
                        >
                          {study.icon}
                        </motion.div>
                        <h3 className="text-lg font-semibold">{study.title}</h3>
                      </motion.div>
                      <p className="text-muted-foreground">
                        {study.description}
                      </p>
                    </motion.div>

                    {/* Optimization metrics */}
                    <div className="grid grid-cols-2 gap-0 border-t">
                      <div className="border-r p-4 text-center">
                        <motion.div
                          className="from-primary bg-gradient-to-r to-blue-600 bg-clip-text text-2xl font-bold text-transparent"
                          initial={{ scale: 1 }}
                          animate={
                            activeCaseStudy === index
                              ? { scale: [1, 1.2, 1] }
                              : { scale: 1 }
                          }
                          transition={{
                            duration: 0.5,
                            repeat: activeCaseStudy === index ? 1 : 0
                          }}
                        >
                          {study.optimizationData.timeReduction}
                        </motion.div>
                        <div className="text-sm text-gray-500">Time Saved</div>
                      </div>
                      <div className="p-4 text-center">
                        <motion.div
                          className="from-primary bg-gradient-to-r to-blue-600 bg-clip-text text-2xl font-bold text-transparent"
                          initial={{ scale: 1 }}
                          animate={
                            activeCaseStudy === index
                              ? { scale: [1, 1.2, 1] }
                              : { scale: 1 }
                          }
                          transition={{
                            duration: 0.5,
                            delay: 0.2,
                            repeat: activeCaseStudy === index ? 1 : 0
                          }}
                        >
                          {study.optimizationData.improvementPercent}%
                        </motion.div>
                        <div className="text-sm text-gray-500">Improvement</div>
                      </div>
                    </div>

                    {/* Experiment reduction indicator */}
                    <div className="border-t p-3">
                      <div className="relative h-2 w-full overflow-hidden rounded-full bg-gray-200">
                        <motion.div
                          className="bg-primary absolute left-0 top-0 h-full"
                          initial={{ width: "100%" }}
                          animate={{
                            width: `${(study.optimizationData.finalPoints / study.optimizationData.startingPoints) * 100}%`
                          }}
                          transition={{ duration: 1, delay: 0.5 }}
                        />
                      </div>
                      <div className="mt-2 flex justify-between text-xs text-gray-500">
                        <span>
                          Reduced from {study.optimizationData.startingPoints}{" "}
                          experiments
                        </span>
                        <span>
                          to just {study.optimizationData.finalPoints}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
            */
          </motion.div>

          {/* Feature highlights */}
          <motion.div
            variants={containerVariants}
            className="mt-16 grid gap-8 md:grid-cols-3"
          >
            <motion.div
              variants={itemVariants}
              className="flex flex-col items-center"
            >
              <div className="bg-primary/10 mb-4 flex size-16 items-center justify-center rounded-full">
                <Beaker className="text-primary size-8" />
              </div>
              <h3 className="from-primary mb-2 bg-gradient-to-r to-blue-600 bg-clip-text text-xl font-semibold text-transparent">
                Intelligent Experiment Design
              </h3>
              <p className="text-muted-foreground text-center">
                Our algorithms suggest the most informative experiments to run
                next, maximizing knowledge gained.
              </p>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="flex flex-col items-center"
            >
              <div className="bg-primary/10 mb-4 flex size-16 items-center justify-center rounded-full">
                <Target className="text-primary size-8" />
              </div>
              <h3 className="from-primary mb-2 bg-gradient-to-r to-blue-600 bg-clip-text text-xl font-semibold text-transparent">
                Rapid Convergence
              </h3>
              <p className="text-muted-foreground text-center">
                Find optimal solutions in complex parameter spaces with
                significantly fewer experimental runs.
              </p>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="flex flex-col items-center"
            >
              <div className="bg-primary/10 mb-4 flex size-16 items-center justify-center rounded-full">
                <LineChart className="text-primary size-8" />
              </div>
              <h3 className="from-primary mb-2 bg-gradient-to-r to-blue-600 bg-clip-text text-xl font-semibold text-transparent">
                Insightful Analytics
              </h3>
              <p className="text-muted-foreground text-center">
                Gain deep understanding of your experimental space through
                advanced visualizations and analysis.
              </p>
            </motion.div>
          </motion.div>
        </motion.div>
      </Container>
    </section>
  )
}
